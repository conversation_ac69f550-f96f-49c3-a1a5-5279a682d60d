<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Console\ConfirmableTrait;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class ClearAllCommand extends Command
{
    use ConfirmableTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:all {--no-optimize : Skip optimization step}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all Laravel caches (config, route, view, application) and optionally optimize the application';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (!$this->confirmToProceed()) {
            return Command::FAILURE;
        }

        $this->info('Clearing all Laravel caches...');

        // Run each cache clearing command
        $commands = [
            'cache:clear' => 'Application cache',
            'config:clear' => 'Configuration cache',
            'route:clear' => 'Route cache',
            'view:clear' => 'View cache',
        ];

        foreach ($commands as $command => $description) {
            $this->executeArtisanCommand($command, $description);
        }

        // Also clear compiled class files
        $this->executeArtisanCommand('clear-compiled', 'Compiled class files');

        // Handle CSRF token issues if the option is specified
        if ($this->option('csrf')) {
            $this->handleCsrfReset();
        }

        // Optimize the application if requested
        if (!$this->option('no-optimize')) {
            $this->info('Optimizing application...');

            $this->executeArtisanCommand('optimize:clear', 'Optimizing class loader');
            $this->executeArtisanCommand('optimize', 'Optimizing application');
        }

        $this->newLine();
        $this->info('All caches have been successfully cleared!');

        return Command::SUCCESS;
    }

    /**
     * Run an Artisan command and show its status
     *
     * @param string $command
     * @param string $description
     * @return void
     */
    protected function executeArtisanCommand(string $command, string $description): void
    {
        $this->output->write("<fg=gray>$description:</> ");

        try {
            Artisan::call($command);
            $this->output->writeln('<fg=green>✓ Done</>');
        } catch (\Exception $e) {
            $this->output->writeln('<fg=red>✗ Failed</>');
            $this->error($e->getMessage());
        }
    }

    /**
     * Handle CSRF token issues by clearing sessions and VerifyCsrfToken middleware
     *
     * @return void
     */
    protected function handleCsrfReset(): void
    {
        $this->info('Resetting CSRF tokens and sessions...');

        // Clear session files
        $sessionPath = storage_path('framework/sessions');
        if (File::exists($sessionPath)) {
            $sessionFiles = File::glob("$sessionPath/*");
            $count = count($sessionFiles);

            foreach ($sessionFiles as $file) {
                File::delete($file);
            }

            $this->info("Cleared $count session files");
        }

        // Refresh APP_KEY if requested
        if ($this->confirm('Do you want to regenerate the application key? This will invalidate all existing tokens and sessions.', false)) {
            $this->executeArtisanCommand('key:generate', 'Regenerating application key');
        }

        $this->info('CSRF token reset complete. Users will need to log in again.');
    }
}
