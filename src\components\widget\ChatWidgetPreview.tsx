import React from 'react';
import WidgetPreview from './WidgetPreview';

interface ChatWidgetPreviewProps {
  settings?: any;
  widgetId?: string;
  config?: any;
  showWelcomeButtons?: boolean;
  preChatFormConfig?: any;
  postChatSurveyConfig?: any;
}

const ChatWidgetPreview: React.FC<ChatWidgetPreviewProps> = ({
  settings,
  widgetId,
  config,
  showWelcomeButtons = false,
  preChatFormConfig,
  postChatSurveyConfig
}) => {
  // If config is provided directly, use it
  if (config) {
    return (
      <WidgetPreview
        config={config}
        showWelcomeButtons={showWelcomeButtons}
        preChatFormConfig={preChatFormConfig}
        postChatSurveyConfig={postChatSurveyConfig}
      />
    );
  }

  // Transform settings to the expected config format
  const transformedConfig = {
    appearance: {
      primaryColor: settings?.primaryColor || '#4f46e5',
      headerBgColor: settings?.headerBgColor || settings?.primaryColor || '#4f46e5',
      textColor: settings?.textColor || '#374151',
      borderRadius: settings?.borderRadius || 12,
      widgetWidth: settings?.widgetWidth || 400,
      widgetHeight: settings?.widgetHeight || 600,
      darkMode: settings?.darkMode || false,
      glassMorphism: settings?.glassMorphism || false,
      shadow: settings?.shadow || 'lg',
      animation: settings?.animation || 'slide'
    },
    general: {
      botName: settings?.headerTitle || settings?.botName || 'AI Assistant',
      welcomeMessage: settings?.welcomeMessage || 'Hello! How can I help you today?',
      placeholderText: settings?.placeholderText || 'Type your message...'
    },
    behavior: {
      welcomeButtons: settings?.welcomeButtons || []
    }
  };

  return <WidgetPreview config={transformedConfig} showWelcomeButtons={showWelcomeButtons} />;
};

export default ChatWidgetPreview;
