import { MessageSquareMore, X, Maximize, Minimize } from 'lucide-react';
import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface WidgetPreviewProps {
  config: any;
  showWelcomeButtons?: boolean;
  preChatFormConfig?: any;
  postChatSurveyConfig?: any;
}

const WidgetPreview = ({ config, showWelcomeButtons = false, preChatFormConfig, postChatSurveyConfig }: WidgetPreviewProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showPreChatForm, setShowPreChatForm] = useState(false);
  const [showPostChatSurvey, setShowPostChatSurvey] = useState(false);
  const [chatStarted, setChatStarted] = useState(false);

  const {
    appearance: {
      primaryColor,
      headerBgColor,
      textColor,
      borderRadius,
      widgetWidth,
      widgetHeight,
      darkMode,
      glassMorphism,
      shadow,
      animation
    },
    general: {
      botName,
      welcomeMessage,
      placeholderText
    },
    behavior: {
      welcomeButtons,
      preChat,
      postChat
    }
  } = config;

  const getShadowClass = () => {
    switch (shadow) {
      case 'sm': return 'shadow-sm';
      case 'md': return 'shadow-md';
      case 'lg': return 'shadow-lg';
      case 'xl': return 'shadow-xl';
      default: return '';
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Handle escape key to close fullscreen and prevent body scroll
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden';
      document.addEventListener('keydown', handleEscape);

      return () => {
        document.body.style.overflow = 'unset';
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isFullscreen]);

  const handleClose = () => {
    setIsMinimized(true);
  };

  const handleOpen = () => {
    setIsMinimized(false);
    // Show pre-chat form if enabled and chat hasn't started
    if (preChat && !chatStarted) {
      setShowPreChatForm(true);
    }
  };

  const handlePreChatSubmit = (data?: any) => {
    console.log('Pre-chat form submitted:', data);
    setShowPreChatForm(false);
    setChatStarted(true);
  };

  const handlePreChatCancel = () => {
    setShowPreChatForm(false);
    setIsMinimized(true);
  };

  const handleEndChat = () => {
    if (postChat) {
      setShowPostChatSurvey(true);
    } else {
      setIsMinimized(true);
    }
  };

  const handlePostChatSubmit = (data: any) => {
    console.log('Post-chat survey submitted:', data);
    setShowPostChatSurvey(false);
    setIsMinimized(true);
    setChatStarted(false);
  };

  const handlePostChatCancel = () => {
    setShowPostChatSurvey(false);
    setIsMinimized(true);
    setChatStarted(false);
  };

  const containerStyles = isFullscreen ? {
    width: '90vw',
    height: '90vh',
    maxWidth: '800px',
    maxHeight: '700px',
    borderRadius: `${borderRadius}px`,
  } : {
    width: `${widgetWidth}px`,
    height: `${widgetHeight}px`,
    borderRadius: `${borderRadius}px`,
    maxWidth: '100%',
    maxHeight: '650px'
  };

  if (isMinimized) {
    return (
      <div className="relative">
        <div
          className="opacity-0 pointer-events-none absolute"
          style={{
            width: `${widgetWidth}px`,
            height: `${widgetHeight}px`,
            visibility: 'hidden'
          }}
        ></div>

        <div
          className="cursor-pointer transition-all hover:scale-105"
          onClick={handleOpen}
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '30px',
            backgroundColor: primaryColor,
            boxShadow: shadow !== 'none' ? '0 4px 20px rgba(0, 0, 0, 0.15)' : 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <MessageSquareMore className="w-6 h-6 text-white" />
        </div>
      </div>
    );
  }

  // Render Pre-Chat Form
  const renderPreChatForm = () => (
    <div
      className={`flex flex-col overflow-hidden ${getShadowClass()} transition-all ${darkMode ? 'bg-gray-800' : 'bg-white'
        } ${glassMorphism ? 'backdrop-blur-md bg-opacity-80' : ''} relative`}
      style={containerStyles}
    >
      {/* Header */}
      <div
        className="p-4 flex items-center justify-between"
        style={{
          backgroundColor: headerBgColor,
          borderTopLeftRadius: `${borderRadius}px`,
          borderTopRightRadius: `${borderRadius}px`,
          ...(glassMorphism && { backdropFilter: 'blur(10px)', backgroundColor: `${headerBgColor}cc` })
        }}
      >
        <div className="flex items-center">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
            style={{ backgroundColor: primaryColor }}
          >
            <MessageSquareMore className="w-5 h-5 text-white" />
          </div>
          <h3 className="font-semibold text-white">{botName}</h3>
        </div>
        <button
          className="text-white hover:bg-white/10 rounded-full p-1"
          onClick={handlePreChatCancel}
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Pre-Chat Form Content */}
      <div className={`flex-1 p-6 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center mb-6">
          <h3 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            {preChatFormConfig?.title || "Welcome! Let's get to know you"}
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {preChatFormConfig?.description || "Please share a few details so we can provide you with better assistance."}
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
              Name *
            </label>
            <input
              type="text"
              placeholder="Your full name"
              className={`w-full py-2 px-3 text-sm rounded-md border outline-none ${darkMode
                ? 'bg-gray-700 text-white border-gray-600 focus:border-blue-400'
                : 'bg-white text-gray-800 border-gray-300 focus:border-blue-300'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            />
          </div>

          <div>
            <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
              Email *
            </label>
            <input
              type="email"
              placeholder="<EMAIL>"
              className={`w-full py-2 px-3 text-sm rounded-md border outline-none ${darkMode
                ? 'bg-gray-700 text-white border-gray-600 focus:border-blue-400'
                : 'bg-white text-gray-800 border-gray-300 focus:border-blue-300'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            />
          </div>

          <div>
            <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
              How can we help you?
            </label>
            <select
              className={`w-full py-2 px-3 text-sm rounded-md border outline-none ${darkMode
                ? 'bg-gray-700 text-white border-gray-600 focus:border-blue-400'
                : 'bg-white text-gray-800 border-gray-300 focus:border-blue-300'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            >
              <option>General Question</option>
              <option>Technical Support</option>
              <option>Billing</option>
              <option>Sales</option>
            </select>
          </div>
        </div>

        <div className="mt-6 flex gap-3">
          <button
            onClick={handlePreChatSubmit}
            className="flex-1 py-2 px-4 text-sm font-medium text-white rounded-md transition-colors"
            style={{
              backgroundColor: primaryColor,
              borderRadius: `${Math.max(6, borderRadius - 4)}px`
            }}
          >
            Start Chat
          </button>
          <button
            onClick={handlePreChatCancel}
            className={`px-4 py-2 text-sm font-medium rounded-md border transition-colors ${darkMode
              ? 'text-gray-300 border-gray-600 hover:bg-gray-700'
              : 'text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
          >
            Maybe Later
          </button>
        </div>
      </div>
    </div>
  );

  // Render Post-Chat Survey
  const renderPostChatSurvey = () => (
    <div
      className={`flex flex-col overflow-hidden ${getShadowClass()} transition-all ${darkMode ? 'bg-gray-800' : 'bg-white'
        } ${glassMorphism ? 'backdrop-blur-md bg-opacity-80' : ''} relative`}
      style={containerStyles}
    >
      {/* Header */}
      <div
        className="p-4 flex items-center justify-between"
        style={{
          backgroundColor: headerBgColor,
          borderTopLeftRadius: `${borderRadius}px`,
          borderTopRightRadius: `${borderRadius}px`,
          ...(glassMorphism && { backdropFilter: 'blur(10px)', backgroundColor: `${headerBgColor}cc` })
        }}
      >
        <div className="flex items-center">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
            style={{ backgroundColor: primaryColor }}
          >
            <MessageSquareMore className="w-5 h-5 text-white" />
          </div>
          <h3 className="font-semibold text-white">{botName}</h3>
        </div>
        <button
          className="text-white hover:bg-white/10 rounded-full p-1"
          onClick={handlePostChatCancel}
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Post-Chat Survey Content */}
      <div className={`flex-1 p-6 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center mb-6">
          <h3 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            {postChatSurveyConfig?.title || "How was your experience?"}
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {postChatSurveyConfig?.description || "Please take a moment to provide feedback on your chat experience."}
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className={`block text-sm font-medium mb-3 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
              How would you rate your experience?
            </label>
            <div className="flex justify-center gap-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  className="text-2xl hover:scale-110 transition-transform"
                  style={{ color: primaryColor }}
                >
                  ⭐
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
              Did we solve your problem?
            </label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input type="radio" name="solved" value="yes" className="mr-2" />
                <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-700'}`}>Yes</span>
              </label>
              <label className="flex items-center">
                <input type="radio" name="solved" value="partially" className="mr-2" />
                <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-700'}`}>Partially</span>
              </label>
              <label className="flex items-center">
                <input type="radio" name="solved" value="no" className="mr-2" />
                <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-700'}`}>No</span>
              </label>
            </div>
          </div>

          <div>
            <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
              Additional comments (optional)
            </label>
            <textarea
              placeholder="Tell us how we can improve..."
              rows={3}
              className={`w-full py-2 px-3 text-sm rounded-md border outline-none resize-none ${darkMode
                ? 'bg-gray-700 text-white border-gray-600 focus:border-blue-400'
                : 'bg-white text-gray-800 border-gray-300 focus:border-blue-300'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            />
          </div>
        </div>

        <div className="mt-6 flex gap-3">
          <button
            onClick={handlePostChatSubmit}
            className="flex-1 py-2 px-4 text-sm font-medium text-white rounded-md transition-colors"
            style={{
              backgroundColor: primaryColor,
              borderRadius: `${Math.max(6, borderRadius - 4)}px`
            }}
          >
            Submit Feedback
          </button>
          <button
            onClick={handlePostChatCancel}
            className={`px-4 py-2 text-sm font-medium rounded-md border transition-colors ${darkMode
              ? 'text-gray-300 border-gray-600 hover:bg-gray-700'
              : 'text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
          >
            Skip
          </button>
        </div>
      </div>
    </div>
  );

  const renderWidgetContent = () => (
    <div
      className={`flex flex-col overflow-hidden ${getShadowClass()} transition-all ${darkMode ? 'bg-gray-800' : 'bg-white'
        } ${glassMorphism ? 'backdrop-blur-md bg-opacity-80' : ''} relative`}
      style={containerStyles}
    >
      {/* Widget Header */}
      <div
        className="p-4 flex items-center justify-between"
        style={{
          backgroundColor: headerBgColor,
          borderTopLeftRadius: `${borderRadius}px`,
          borderTopRightRadius: `${borderRadius}px`,
          ...(glassMorphism && { backdropFilter: 'blur(10px)', backgroundColor: `${headerBgColor}cc` })
        }}
      >
        <div className="flex items-center">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
            style={{ backgroundColor: primaryColor }}
          >
            <MessageSquareMore className="w-5 h-5 text-white" />
          </div>
          <h3 className="font-semibold text-white">{botName}</h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            className="text-white hover:bg-white/10 rounded-full p-1"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? (
              <Minimize className="w-5 h-5" />
            ) : (
              <Maximize className="w-5 h-5" />
            )}
          </button>
          <button
            className="text-white hover:bg-white/10 rounded-full p-1"
            onClick={handleClose}
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Chat Area */}
      <div
        className={`flex-1 p-4 overflow-y-auto ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}
        style={{ color: textColor }}
      >
        {/* Bot Message */}
        <div className="mb-4 flex">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
            style={{ backgroundColor: primaryColor }}
          >
            <MessageSquareMore className="w-4 h-4 text-white" />
          </div>
          <div
            className={`py-3 px-4 rounded-lg max-w-[80%] ${darkMode ? 'bg-gray-800' : 'bg-white'}`}
            style={{ borderRadius: `${Math.max(8, borderRadius - 2)}px` }}
          >
            <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              {welcomeMessage}
            </p>

            {/* Quick reply buttons */}
            {showWelcomeButtons && welcomeButtons && welcomeButtons.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {welcomeButtons.map((button: { label: string, value: string }, idx: number) => (
                  <button
                    key={idx}
                    className="px-3 py-1 text-xs rounded-full transition-colors"
                    style={{
                      backgroundColor: darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                      color: darkMode ? 'rgba(255,255,255,0.9)' : 'rgba(0,0,0,0.8)'
                    }}
                  >
                    {button.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Sample user message */}
        <div className="mb-4 flex justify-end">
          <div
            className="py-3 px-4 rounded-lg max-w-[80%]"
            style={{
              backgroundColor: primaryColor,
              borderRadius: `${Math.max(8, borderRadius - 2)}px`
            }}
          >
            <p className="text-sm text-white">
              Hello! I have a question about your services.
            </p>
          </div>
        </div>

        {/* Sample bot response */}
        <div className="mb-4 flex">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
            style={{ backgroundColor: primaryColor }}
          >
            <MessageSquareMore className="w-4 h-4 text-white" />
          </div>
          <div
            className={`py-3 px-4 rounded-lg max-w-[80%] ${darkMode ? 'bg-gray-800' : 'bg-white'}`}
            style={{ borderRadius: `${Math.max(8, borderRadius - 2)}px` }}
          >
            <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              Of course! I'd be happy to help you with information about our services. What specific aspect are you interested in learning more about?
            </p>
          </div>
        </div>
      </div>

      {/* Input Area */}
      <div
        className={`p-3 border-t ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
        style={{
          borderBottomLeftRadius: `${borderRadius}px`,
          borderBottomRightRadius: `${borderRadius}px`
        }}
      >
        <div className="mb-3 flex justify-center gap-2">
          {chatStarted && (
            <button
              onClick={handleEndChat}
              className={`px-4 py-2 text-xs font-medium rounded-md border transition-colors ${darkMode
                ? 'text-gray-300 border-gray-600 hover:bg-gray-700'
                : 'text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            >
              End Chat
            </button>
          )}
          {preChat && !chatStarted && (
            <button
              onClick={() => setShowPreChatForm(true)}
              className={`px-4 py-2 text-xs font-medium rounded-md border transition-colors ${darkMode
                ? 'text-blue-300 border-blue-600 hover:bg-blue-700/20'
                : 'text-blue-700 border-blue-300 hover:bg-blue-50'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            >
              📝 Test Pre-Chat Form
            </button>
          )}
          {postChat && chatStarted && (
            <button
              onClick={() => setShowPostChatSurvey(true)}
              className={`px-4 py-2 text-xs font-medium rounded-md border transition-colors ${darkMode
                ? 'text-amber-300 border-amber-600 hover:bg-amber-700/20'
                : 'text-amber-700 border-amber-300 hover:bg-amber-50'
                }`}
              style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
            >
              ⭐ Test Survey
            </button>
          )}
        </div>
        <div className="flex items-center">
          <input
            type="text"
            placeholder={placeholderText}
            className={`flex-1 py-2 px-3 text-sm rounded-md border outline-none ${darkMode
              ? 'bg-gray-700 text-white border-gray-600 focus:border-blue-400'
              : 'bg-gray-50 text-gray-800 border-gray-300 focus:border-blue-300'
              }`}
            style={{ borderRadius: `${Math.max(6, borderRadius - 4)}px` }}
          />
          <button
            className="ml-2 p-2 rounded-md"
            style={{
              backgroundColor: primaryColor,
              borderRadius: `${Math.max(6, borderRadius - 4)}px`
            }}
          >
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );

  // Render fullscreen modal using portal
  const renderFullscreenModal = () => {
    if (!isFullscreen) return null;

    return createPortal(
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999
        }}
        onClick={(e) => {
          // Close fullscreen when clicking outside the widget
          if (e.target === e.currentTarget) {
            setIsFullscreen(false);
          }
        }}
      >
        <div className="max-w-[90vw] max-h-[90vh] flex items-center justify-center p-4">
          {renderWidgetContent()}
        </div>
      </div>,
      document.body
    );
  };

  // Determine what to render based on current state
  const getMainContent = () => {
    if (showPreChatForm) {
      return renderPreChatForm();
    }
    if (showPostChatSurvey) {
      return renderPostChatSurvey();
    }
    return renderWidgetContent();
  };

  return (
    <>
      {getMainContent()}
      {renderFullscreenModal()}
    </>
  );
};

export default WidgetPreview;
