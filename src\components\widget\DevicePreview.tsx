
import { ReactNode, useState, useEffect } from 'react';
import { Maximize, Minimize } from 'lucide-react';

interface DevicePreviewProps {
  device: 'desktop' | 'tablet' | 'mobile';
  children: ReactNode;
}

const DevicePreview = ({ device, children }: DevicePreviewProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const getDeviceStyles = () => {

    switch (device) {
      case 'desktop':
        return {
          wrapper: 'max-w-full mx-auto',
          scale: 1,
          background: 'bg-transparent',
        };
      case 'tablet':
        return {
          wrapper: 'max-w-[768px] mx-auto border-8 border-gray-800 rounded-xl overflow-hidden',
          scale: 0.8,
          background: 'bg-gradient-to-b from-gray-100 to-gray-200',
        };
      case 'mobile':
        return {
          wrapper: 'max-w-[375px] mx-auto border-[12px] border-gray-800 rounded-[36px] overflow-hidden',
          scale: 0.65,
          background: 'bg-gradient-to-b from-gray-100 to-gray-200',
        };
      default:
        return {
          wrapper: 'max-w-full mx-auto',
          scale: 1,
          background: 'bg-transparent',
        };
    }
  };

  const { wrapper, scale, background } = getDeviceStyles();

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Handle escape key to close fullscreen
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isFullscreen]);

  const deviceContent = (
    <div className={`${wrapper} ${background} transition-all duration-300 relative`}>
      <div
        className="device-content"
        style={{
          transform: `scale(${scale})`,
          transformOrigin: 'center center',
          transition: 'transform 0.3s ease'
        }}
      >
        {children}
      </div>

      <button
        onClick={toggleFullscreen}
        className="absolute top-2 right-2 p-2 bg-gray-800/50 hover:bg-gray-800/80 rounded-full text-white transition-colors z-10"
      >
        {isFullscreen ? (
          <Minimize className="h-4 w-4" />
        ) : (
          <Maximize className="h-4 w-4" />
        )}
      </button>
    </div>
  );

  // Return with modal overlay for fullscreen
  if (isFullscreen) {
    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        onClick={(e) => {
          // Close fullscreen when clicking outside the device
          if (e.target === e.currentTarget) {
            setIsFullscreen(false);
          }
        }}
      >
        <div className="max-w-[90vw] max-h-[90vh] overflow-auto">
          {deviceContent}
        </div>
      </div>
    );
  }

  return deviceContent;
};

export default DevicePreview;
