/**
 * DEPRECATED: This service is being consolidated with utils/widgetService.ts
 * 
 * For new code, please use widgetService from "@/utils/widgetService" instead.
 * This file is maintained for backward compatibility but will be removed in a future update.
 */

import { widgetService } from "@/utils/widgetService";
import type { Widget, WidgetSettings } from "@/utils/widgetService";

// Re-export the interface types
export type WidgetConfig = Widget;

// Re-export the service with a wrapper that maps to the new implementation
export const widgetConfigService = {
  getDefaultWidgetConfig: async (): Promise<WidgetConfig> => {
    try {
      // Try to find the default widget
      const response = await widgetService.getAllWidgets();
      const widgets = response.data || [];
      const defaultWidget = widgets.find(w => w.is_active === true);

      if (defaultWidget) {
        return defaultWidget;
      }

      // If no default found, return a fallback configuration
      return {
        name: "Chat Assistant",
        is_active: true,
        settings: {
          primaryColor: "#6366f1",
          position: "bottom-right",
          headerTitle: "Chat Assistant",
        }
      };
    } catch (error) {
      console.error("Error fetching widget configuration", error);

      // Return a fallback configuration
      return {
        name: "Chat Assistant",
        is_active: true,
        settings: {
          primaryColor: "#6366f1",
          position: "bottom-right",
          headerTitle: "Chat Assistant",
        }
      };
    }
  },

  createWidgetConfig: async (config: Omit<WidgetConfig, "id" | "created_at">): Promise<WidgetConfig> => {
    const response = await widgetService.createWidget(config as Widget);
    return response.data;
  },

  updateWidgetConfig: async (id: string | number, config: Partial<WidgetConfig>): Promise<WidgetConfig> => {
    const response = await widgetService.updateWidget(Number(id), config);
    return response.data;
  },

  getAllWidgetConfigs: async (): Promise<WidgetConfig[]> => {
    const response = await widgetService.getAllWidgets();
    return response.data || [];
  },

  deleteWidgetConfig: async (id: string | number): Promise<boolean> => {
    await widgetService.deleteWidget(Number(id));
    return true;
  }
};

export default widgetConfigService;
