# Artisan Commands

This document lists custom Artisan commands available in the application.

## Cache and Optimization Commands

### `clear:all`

This command clears all Laravel caches and optionally optimizes the application in a single operation.

**Usage:**
```bash
php artisan clear:all
```

**What it does:**
1. Clears application cache (`cache:clear`)
2. Clears configuration cache (`config:clear`)
3. Clears route cache (`route:clear`)
4. Clears view cache (`view:clear`)
5. Clears compiled class files (`clear-compiled`)
6. Optimizes the application (`optimize` and `optimize:clear`)

**Options:**
- `--no-optimize`: Skip the optimization steps
- `--csrf`: Also reset CSRF tokens and session files (helps fix CSRF token mismatch errors)

**Examples:**
```bash
# Clear all caches without optimization
php artisan clear:all --no-optimize

# Clear all caches and reset CSRF tokens
php artisan clear:all --csrf
```

**When to use:**
- After deployment to production
- When making configuration changes
- When experiencing unexpected behavior that might be due to cached files
- During development to ensure changes are reflected
- When facing CSRF token mismatch errors

**Warning:**
When running this command in production or staging environments, you'll be asked to confirm before proceeding.

## Troubleshooting CSRF Token Mismatch

If you encounter "CSRF token mismatch" errors, try these solutions:

1. **Clear the browser cache and cookies** for your application domain

2. **Reset the session state and CSRF tokens**:
   ```bash
   php artisan clear:all --csrf
   ```

3. **Check your frontend code** to ensure it's properly sending the CSRF token:
   - For AJAX requests, make sure you're including the `X-CSRF-TOKEN` header
   - For forms, ensure you have the `@csrf` directive in your Blade templates

4. **Verify your CORS configuration** in `config/cors.php`:
   - Ensure `paths` includes `'sanctum/csrf-cookie'`
   - Set `supports_credentials` to `true`
   - Make sure your frontend domain is included in `allowed_origins`

5. **Check Session Configuration**:
   - Make sure `SESSION_DOMAIN` in your `.env` file matches your domain
   - Set `SESSION_SAME_SITE` to `lax` or `none` (with secure cookies)

## Scraping Commands

### `scrape:scheduled`

Runs scheduled web scraping tasks based on the schedule defined in the database.

**Usage:**
```bash
php artisan scrape:scheduled
```

This command is automatically scheduled to run every 15 minutes via the task scheduler. 
